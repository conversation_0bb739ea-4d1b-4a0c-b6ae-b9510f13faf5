package com.faw.sa0214.po;

import com.faw.sa0214.base.DBEnum;
import lombok.Getter;

/**
 * 数据库连接信息类
 *
 * <AUTHOR>
 * @date 2025/08/27
 */
@Getter
public class DatabaseInfo {

    private final String hostname;

    private final int port;

    private final String database;

    private final String schema;

    private final String username;

    private final String password;
    // mysql, kingbase
    private final String databaseType;

    public DatabaseInfo(String hostname, int port, String database,
                        String username, String password, String databaseType) {
        this.hostname = hostname;
        this.port = port;
        this.database = database;
        this.username = username;
        this.password = password;
        this.databaseType = databaseType;
        this.schema = null;
    }

    public DatabaseInfo(String hostname, int port, String database, String schema,
                        String username, String password, String databaseType) {
        this.hostname = hostname;
        this.port = port;
        this.schema = schema;
        this.database = database;
        this.username = username;
        this.password = password;
        this.databaseType = databaseType;
    }

    /**
     * 获取JDBC URL
     */
    public String getJdbcUrl() {
        switch (databaseType.toLowerCase()) {
            case "mysql":
                return String.format("*****************************************************",
                        hostname, port, database);
            case "kingbase":
                return String.format("jdbc:kingbase8://%s:%d/%s", hostname, port, database);
            default:
                throw new IllegalArgumentException("Unsupported database type: " + databaseType);
        }
    }

    /**
     * 获取JDBC驱动类名
     */
    public String getDriverClass() {
        switch (databaseType.toLowerCase()) {
            case "mysql":
                return DBEnum.MYSQL.getDriver();
            case "kingbase":
                return DBEnum.KINGBAE.getDriver();
            default:
                throw new IllegalArgumentException("Unsupported database type: " + databaseType);
        }
    }
}
