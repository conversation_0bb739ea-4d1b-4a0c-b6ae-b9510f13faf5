package com.faw.sa0214.config;

import com.faw.sa0214.po.DatabaseInfo;
import com.faw.sa0214.po.TableInfo;

/**
 * 数据库配置类
 * 用于管理源数据库和目标数据库的连接信息
 */
public class LocalDatabaseConfig {


    // 目标数据库配置
    public static final DatabaseInfo TARGET_DATABASE = new DatabaseInfo(
            System.getProperty("target.hostname", "localhost"),
            Integer.parseInt(System.getProperty("target.port", "54321")),
            System.getProperty("target.database", "ep_rebate"),
            System.getProperty("target.schema", "demo"),
            System.getProperty("target.username", "root"),
            System.getProperty("target.password", "123456"),
            "kingbase"
    );

    // 源数据库配置
    public static final DatabaseInfo DEMO = new DatabaseInfo(
            System.getProperty("demo.hostname", "localhost"),
            Integer.parseInt(System.getProperty("demo.port", "54321")),
            "msa_user",
            "demo",
            System.getProperty("demo.username", "root"),
            System.getProperty("demo.password", "root"),
            "kingbase"
    );


    // 所有需要同步的表配置
    public static final TableInfo[] TABLE_CONFIGS = {
            new TableInfo(DEMO, new String[]{
                    "demo"
            })
    };
}
