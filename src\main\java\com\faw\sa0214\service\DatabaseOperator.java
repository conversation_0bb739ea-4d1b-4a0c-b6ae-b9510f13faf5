package com.faw.sa0214.service;

import com.alibaba.fastjson.JSONObject;
import com.faw.sa0214.po.DatabaseInfo;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 数据库操作工具类（使用 HikariCP 连接池）
 * 支持动态切换目标数据库
 */
@Slf4j
public class DatabaseOperator {

    // 使用 AtomicReference 保证线程安全地替换数据源
    private static final AtomicReference<HikariDataSource> dataSourceRef = new AtomicReference<>();

    /**
     * 设置目标数据库配置，并初始化 HikariCP 连接池
     */
    public static void setTargetDatabase(DatabaseInfo database) {
        // 关闭旧的数据源
        HikariDataSource oldDs = dataSourceRef.get();
        if (oldDs != null && !oldDs.isClosed()) {
            oldDs.close();
        }

        // 创建新的数据源
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(database.getJdbcUrl());
        config.setUsername(database.getUsername());
        config.setPassword(database.getPassword());
        config.setDriverClassName(database.getDriverClass());

        // 基本配置
        config.setAutoCommit(true);
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(2);
        config.setConnectionTimeout(30_000);  // 30秒
        config.setIdleTimeout(600_000);       // 10分钟
        config.setMaxLifetime(1_800_000);     // 30分钟
        config.setPoolName("TargetDBPool");

        // 启用连接测试
        config.setConnectionTestQuery("SELECT 1");

        HikariDataSource newDs = new HikariDataSource(config);
        dataSourceRef.set(newDs);

        log.info("已切换到目标数据库: {}", database.getJdbcUrl());
    }

    /**
     * 获取数据库连接（从 HikariCP 池中获取）
     */
    private static Connection getConnection() throws SQLException {
        HikariDataSource ds = dataSourceRef.get();
        if (ds == null || ds.isClosed()) {
            throw new SQLException("数据库连接池未初始化，请先调用 setTargetDatabase()");
        }
        return ds.getConnection();
    }

    /**
     * 执行 INSERT 操作
     */
    public static void executeInsert(String tableName, JSONObject data) {
        if (data == null || data.isEmpty()) {
            log.warn("INSERT数据为空，跳过表: {}", tableName);
            return;
        }

        try (Connection conn = getConnection()) {
            StringBuilder columns = new StringBuilder();
            StringBuilder values = new StringBuilder();

            for (String key : data.keySet()) {
                if (columns.length() > 0) {
                    columns.append(", ");
                    values.append(", ");
                }
                columns.append(key);
                values.append("?");
            }

            String sql = String.format("INSERT INTO %s (%s) VALUES (%s)",
                    tableName, columns.toString(), values.toString());

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                int paramIndex = 1;
                for (String key : data.keySet()) {
                    stmt.setObject(paramIndex++, data.get(key));
                }

                int rowsAffected = stmt.executeUpdate();
                log.info("INSERT成功: 表={}, 影响行数={}", tableName, rowsAffected);
            }
        } catch (SQLException e) {
            log.error("INSERT失败: 表={}, 数据={}", tableName, data, e);
        }
    }

    /**
     * 执行 UPDATE 操作
     */
    public static void executeUpdate(String tableName, JSONObject before, JSONObject after) {
        if (after == null || after.isEmpty()) {
            log.warn("UPDATE数据为空，跳过表: {}", tableName);
            return;
        }

        try (Connection conn = getConnection()) {
            StringBuilder setClause = new StringBuilder();
            StringBuilder whereClause = new StringBuilder();

            for (String key : after.keySet()) {
                if (setClause.length() > 0) {
                    setClause.append(", ");
                }
                setClause.append(key).append(" = ?");
            }

            if (before != null && !before.isEmpty()) {
                for (String key : before.keySet()) {
                    if (whereClause.length() > 0) {
                        whereClause.append(" AND ");
                    }
                    whereClause.append(key).append(" = ?");
                }
            } else {
                Object id = after.get("id");
                if (id != null) {
                    whereClause.append("id = ?");
                } else {
                    log.error("UPDATE操作缺少WHERE条件: 表={}", tableName);
                    return;
                }
            }

            String sql = String.format("UPDATE %s SET %s WHERE %s",
                    tableName, setClause.toString(), whereClause.toString());

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                int paramIndex = 1;
                for (String key : after.keySet()) {
                    stmt.setObject(paramIndex++, after.get(key));
                }
                if (before != null && !before.isEmpty()) {
                    for (String key : before.keySet()) {
                        stmt.setObject(paramIndex++, before.get(key));
                    }
                } else {
                    stmt.setObject(paramIndex++, after.get("id"));
                }

                int rowsAffected = stmt.executeUpdate();
                log.info("UPDATE成功: 表={}, 影响行数={}", tableName, rowsAffected);
            }
        } catch (SQLException e) {
            log.error("UPDATE失败: 表={}, before={}, after={}", tableName, before, after, e);
        }
    }

    /**
     * 执行 DELETE 操作
     */
    public static void executeDelete(String tableName, JSONObject data) {
        if (data == null || data.isEmpty()) {
            log.warn("DELETE数据为空，跳过表: {}", tableName);
            return;
        }

        try (Connection conn = getConnection()) {
            StringBuilder whereClause = new StringBuilder();
            List<Object> values = new ArrayList<>();

            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (whereClause.length() > 0) {
                    whereClause.append(" AND ");
                }

                if (value == null) {
                    whereClause.append(key).append(" IS NULL");
                } else {
                    whereClause.append(key).append(" = ?");
                    values.add(value);
                }
            }

            String[] parts = tableName.split("\\.", 2);
            String quotedTableName;
            if (parts.length == 2) {
                quotedTableName = "\"" + parts[0] + "\".\"" + parts[1] + "\"";
            } else {
                quotedTableName = "public.\"" + tableName + "\"";
            }

            String sql = "DELETE FROM " + quotedTableName + " WHERE " + whereClause.toString();

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                int paramIndex = 1;
                for (Object value : values) {
                    stmt.setObject(paramIndex++, value);
                }

                int rowsAffected = stmt.executeUpdate();
                log.info("DELETE成功: 表={}, 影响行数={}", tableName, rowsAffected);
                if (rowsAffected == 0) {
                    log.warn("DELETE未删除任何行，可能目标库无匹配记录: {}", data);
                }
            }
        } catch (SQLException e) {
            log.error("DELETE失败: 表={}, 数据={}", tableName, data, e);
        }
    }

    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && conn.isValid(5);
        } catch (SQLException e) {
            log.error("数据库连接测试失败", e);
            return false;
        }
    }

    /**
     * 关闭当前连接池
     */
    public static void closeAllConnections() {
        HikariDataSource ds = dataSourceRef.getAndSet(null);
        if (ds != null && !ds.isClosed()) {
            ds.close();
            log.info("HikariCP 连接池已关闭");
        }
    }

    /**
     * 根据 ID 更新记录
     */
    public static int updateById(String schema, String table, Long id, JSONObject rowData) throws Exception {
        try (Connection conn = getConnection()) {
            List<String> setFields = new ArrayList<>();
            List<Object> values = new ArrayList<>();

            for (String key : rowData.keySet()) {
                if ("id".equalsIgnoreCase(key)) continue;
                setFields.add("`" + key + "` = ?");
                values.add(rowData.get(key));
            }

            if (setFields.isEmpty()) return 0;

            String sql = "UPDATE `" + schema + "`.`" + table + "` SET " + String.join(", ", setFields) + " WHERE id = ?";
            values.add(id);

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                for (int i = 0; i < values.size(); i++) {
                    stmt.setObject(i + 1, values.get(i));
                }
                return stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("更新失败: schema={}, table={}, id={}", schema, table, id, e);
            throw new Exception("数据库更新失败", e);
        }
    }
}