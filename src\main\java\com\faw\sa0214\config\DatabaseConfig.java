package com.faw.sa0214.config;

import com.faw.sa0214.base.DBEnum;
import com.faw.sa0214.po.DatabaseInfo;
import com.faw.sa0214.po.TableInfo;

/**
 * 数据库配置类
 * 用于管理源数据库和目标数据库的连接信息
 */
public class DatabaseConfig {


    // 目标数据库配置
    public static final DatabaseInfo TARGET_DATABASE = new DatabaseInfo(
            System.getProperty("target.hostname", "************"),
            Integer.parseInt(System.getProperty("target.port", "33321")),
            System.getProperty("target.database", "tdsdata"),
            System.getProperty("target.username", "xsycpeyi"),
            System.getProperty("target.password", "YG_Gzt_User!#202412"),
            DBEnum.KINGBAE.getName()
    );

    // 源数据库配置
    public static final DatabaseInfo CYXDMS_RETAIL = new DatabaseInfo(
            System.getProperty("cyxdms.hostname", "**************"),
            Integer.parseInt(System.getProperty("cyxdms.port", "10200")),
            "cyxdms_retail",
            System.getProperty("cyxdms.username", "flink_user1"),
            System.getProperty("cyxdms.password", "flink_password1"),
            DBEnum.MYSQL.getName()
    );

    public static final DatabaseInfo EP_SAL = new DatabaseInfo(
            System.getProperty("epsal.hostname", "**************"),
            Integer.parseInt(System.getProperty("epsal.port", "14503")),
            "ep_sal",
            System.getProperty("epsal.username", "flink_user2"),
            System.getProperty("epsal.password", "flink_password2"),
            DBEnum.MYSQL.getName()
    );

    public static final DatabaseInfo EP_SC_SYS = new DatabaseInfo(
            System.getProperty("epscsys.hostname", "**************"),
            Integer.parseInt(System.getProperty("epscsys.port", "14513")),
            "ep_sc_sys",
            System.getProperty("epscsys.username", "flink_user3"),
            System.getProperty("epscsys.password", "flink_password3"),
            DBEnum.MYSQL.getName()
    );

    public static final DatabaseInfo EP_SC_VC = new DatabaseInfo(
            System.getProperty("epscvc.hostname", "**************"),
            Integer.parseInt(System.getProperty("epscvc.port", "14511")),
            "ep_sc_vc",
            System.getProperty("epscvc.username", "flink_user4"),
            System.getProperty("epscvc.password", "flink_password4"),
            DBEnum.MYSQL.getName()
    );


    // 所有需要同步的表配置
    public static final TableInfo[] TABLE_CONFIGS = {
            new TableInfo(CYXDMS_RETAIL, new String[]{
                    "tt_invoice_upload",
                    "tt_invoice_upload_child",
                    "tt_orders_upload_log",
                    "tt_orders_upload_log_detail",
                    "tt_sales_order_detail",
                    "tt_sales_order_vin",
                    "tt_sales_orders"
            }),

            new TableInfo(EP_SAL, new String[]{
                    "ck_dms_tt_orders_upload_log",
                    "ck_dms_tt_orders_upload_log_detail",
                    "mdac001d", "mdac075", "mdac100d1", "mdac100d2", "mdac100d3",
                    "mdac300", "mdac301c", "mdac302", "mdai152",
                    "salb001", "salb011", "salb021", "salb026", "salb026_d",
                    "salb027", "salb027_d", "salb027_h", "salb030c", "salb030d",
                    "salb040c", "salb040d", "salb040d1", "salb141c", "salb141d",
                    "salc062", "salc106c", "salr021", "salr052",
                    "sptb001cd", "sptb021", "sptb022", "sptb022_d", "sptb022_h",
                    "sptb050cd", "spti006", "sysc060d1", "sysi051"
            }),

            new TableInfo(EP_SC_SYS, new String[]{
                    "mdac100", "sysc005"
            }),

            new TableInfo(EP_SC_VC, new String[]{
                    "v_product"
            })
    };
}
