package com.faw.sa0214.config;

import com.faw.sa0214.po.DatabaseInfo;
import com.faw.sa0214.po.TableInfo;

/**
 * 数据库配置类
 * 用于管理源数据库和目标数据库的连接信息
 */
public class UatDatabaseConfig {


    // 目标数据库配置
    public static final DatabaseInfo TARGET_DATABASE = new DatabaseInfo(
            System.getProperty("target.hostname", "*************"),
            Integer.parseInt(System.getProperty("target.port", "33321")),
            System.getProperty("target.database", "yg_gzt_db"),
            System.getProperty("target.schema", "msa_tak"),
            System.getProperty("target.username", "yg_gzt_user"),
            System.getProperty("target.password", "YG_Gzt_User!#202412"),
            "kingbase"
    );

    // 源数据库配置
    public static final DatabaseInfo DEMO = new DatabaseInfo(
            System.getProperty("demo.hostname", "*************"),
            Integer.parseInt(System.getProperty("demo.port", "33321")),
            "yg_gzt_db",
            "msa_user",
            System.getProperty("demo.username", "yg_gzt_user"),
            System.getProperty("demo.password", "YG_Gzt_User!#202502"),
            "kingbase"
    );


    // 所有需要同步的表配置
    public static final TableInfo[] TABLE_CONFIGS = {
            new TableInfo(DEMO, new String[]{
                    "msa_integrate_user"
            })
    };
}
