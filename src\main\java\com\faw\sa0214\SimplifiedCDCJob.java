package com.faw.sa0214;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.faw.sa0214.base.DBEnum;
import com.faw.sa0214.config.LocalDatabaseConfig;
import com.faw.sa0214.config.UatDatabaseConfig;
import com.faw.sa0214.po.DatabaseInfo;
import com.faw.sa0214.po.TableInfo;
import com.faw.sa0214.service.DatabaseOperator;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.ververica.cdc.debezium.DebeziumSourceFunction;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import com.ververica.cdc.debezium.Validator;
import io.debezium.connector.kingbase.connector.KingbaseConnector;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.Properties;

/**
 * 简化的 Flink CDC 多源同步作业
 * 基于 DataStream API，自动处理表结构，表名和字段完全一致
 */
@Slf4j
public class SimplifiedCDCJob {

    // 获取配置类信息
    private final static TableInfo[] tableInfos = LocalDatabaseConfig.TABLE_CONFIGS;
    private final static DatabaseInfo targetDatabase = LocalDatabaseConfig.TARGET_DATABASE;

    public static void main(String[] args) throws Exception {
        // 0. 设置目标数据库
        DatabaseOperator.setTargetDatabase(targetDatabase);

        // 1. 初始化执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(2);
        env.disableOperatorChaining();
        env.enableCheckpointing(10000); // 每10秒做一次Checkpoint

        // 2. 为每个源数据库创建CDC数据流
        for (TableInfo tableInfo : tableInfos) {
            createCDCDataStream(env, tableInfo);
        }

        // 3. 启动作业
        log.info("启动多源CDC同步作业...");
        env.execute("Simplified Multi-Source CDC Sync Job");
    }

    /**
     * 为源数据库创建CDC数据流
     */
    private static void createCDCDataStream(StreamExecutionEnvironment env,
                                            TableInfo tableInfo) {

        DatabaseInfo sourceDb = tableInfo.getSourceDatabase();
        String[] tableNames = tableInfo.getTableNames();

        try {

            // 构建完整表名列表 (database.table格式)
            String[] fullTableNames = new String[tableNames.length];
            for (int i = 0; i < tableNames.length; i++) {
                fullTableNames[i] = sourceDb.getDatabase() + "." + tableNames[i];
            }

            Properties dbzProps = new Properties();
            // JDBC 相关参数（通过 Debezium 传递）
            dbzProps.setProperty("connection.timezone", "Asia/Shanghai");
            dbzProps.setProperty("connect.timeout.ms", "30000");
            dbzProps.setProperty("connect.keep.alive", "true");

            // 可选：Debezium 快照参数
            dbzProps.setProperty("snapshot.mode", "initial");

            DataStream<String> cdcStream;
            if (DBEnum.MYSQL.getName().equals(sourceDb.getDatabaseType())) {
                // 创建MySQL CDC Source
                MySqlSource<String> mySqlSource = MySqlSource.<String>builder()
                        .hostname(sourceDb.getHostname())
                        .port(sourceDb.getPort())
                        .databaseList(sourceDb.getDatabase())
                        .tableList(fullTableNames)
                        .username(sourceDb.getUsername())
                        .password(sourceDb.getPassword())
                        .debeziumProperties(dbzProps)
                        .startupOptions(StartupOptions.initial()) // 全量+增量同步
                        .deserializer(new JsonDebeziumDeserializationSchema())
                        .includeSchemaChanges(true)
                        .build();

                // 创建数据流并处理
                String sourceName = sourceDb.getDatabase() + " CDC Source";
                cdcStream = env.fromSource(mySqlSource, WatermarkStrategy.noWatermarks(), sourceName);
            } else {
                // 设置 Debezium 属性（对应人大金仓示例）
                dbzProps.setProperty("connector.class", KingbaseConnector.class.getCanonicalName());
                dbzProps.setProperty("database.server.name", "kingbase_cdc_source"); // 逻辑服务器名，唯一标识
                dbzProps.setProperty("database.hostname", sourceDb.getHostname());
                dbzProps.setProperty("database.dbname", sourceDb.getDatabase());     // 注意：这里是 dbname 而不是 database
                dbzProps.setProperty("database.port", String.valueOf(sourceDb.getPort()));
                dbzProps.setProperty("database.user", sourceDb.getUsername());
                dbzProps.setProperty("database.password", sourceDb.getPassword());
                dbzProps.setProperty("schema.include.list", String.join(",", sourceDb.getSchema())); // 提取 schema 列表
                dbzProps.setProperty("table.include.list", String.join(",", fullTableNames)); // 显式包含表
                dbzProps.setProperty("publication.autocreate.mode", "all_tables"); // 自动创建 publication
                dbzProps.setProperty("slot.name", "flink_cdc_slot");               // 复制槽名称
                dbzProps.setProperty("snapshot.mode", "initial");                  // 初始快照 + 增量

                // 创建 DebeziumSourceFunction（等价于 MySqlSource 的角色）
                DebeziumSourceFunction<String> kingbaseSource = new DebeziumSourceFunction<>(
                        new JsonDebeziumDeserializationSchema(), // 输出为 JSON 字符串
                        dbzProps,
                        null,
                        Validator.getDefaultValidator()
                );

                // 创建数据流（fromSource 风格，Flink 1.12+ 推荐）
                String sourceName = sourceDb.getDatabase() + " CDC Source";
                cdcStream = env.addSource(kingbaseSource)
                        .name(sourceName)
                        .setParallelism(1);
            }

            // 处理CDC事件
            cdcStream
                    .filter(json -> json != null && !json.isEmpty())
                    .map(new CDCEventProcessor())
                    .name("Process " + sourceDb.getDatabase() + " Events")
                    .setParallelism(1);

            log.info("成功创建CDC数据流: {} (包含 {} 个表)", sourceDb.getDatabase(), tableNames.length);

        } catch (Exception e) {
            log.error("创建CDC数据流失败: {}", sourceDb.getDatabase(), e);
        }
    }

    /**
     * CDC事件处理器
     */
    public static class CDCEventProcessor implements MapFunction<String, String> {

        @Override
        public String map(String jsonData) throws Exception {
            try {
                log.info("接收到CDC事件: {}", jsonData);

                // 解析CDC事件
                JSONObject cdcEvent = JSON.parseObject(jsonData);

                // 提取关键信息
                String operation = cdcEvent.getString("op"); // c=create, u=update, d=delete, r=read
                JSONObject source = cdcEvent.getJSONObject("source");
                String database = source.getString("db");
                String table = source.getString("table");

                // 根据操作类型处理数据
                switch (operation) {
                    case "c": // INSERT
                    case "r": // READ (初始快照)
                        handleInsert(cdcEvent, database, table);
                        break;
                    case "u": // UPDATE
                        handleUpdate(cdcEvent, database, table);
                        break;
                    case "d": // DELETE
                        handleDelete(cdcEvent, database, table);
                        break;
                    default:
                        log.warn("未知的操作类型: {}", operation);
                }
                return jsonData;

            } catch (Exception e) {
                log.error("处理CDC事件失败: {}", jsonData, e);
                return null;
            }
        }

        private void handleInsert(JSONObject cdcEvent, String database, String table) {

            String schema = targetDatabase.getSchema();

            JSONObject after = cdcEvent.getJSONObject("after");
            log.info("处理INSERT事件: {}.{}, 数据: {}", database, table, after);

            try {
                // 直接插入到目标数据库的同名表
                DatabaseOperator.executeInsert(schema + "." + table, after);
            } catch (Exception e) {
                log.error("INSERT操作失败: {}.{}", database, table, e);
                // 可以选择重试或记录错误
            }
        }

        private void handleUpdate(JSONObject cdcEvent, String database, String table) {

            String schema = targetDatabase.getSchema();

            JSONObject before = cdcEvent.getJSONObject("before");
            JSONObject after = cdcEvent.getJSONObject("after");

            log.info("处理UPDATE事件: {}.{}, 变更前: {}, 变更后: {}", database, table, before, after);

            Object oldIdObj = before.get("id");
            Object newIdObj = after.get("id");

            if (oldIdObj == null) {
                log.warn("UPDATE事件缺少旧主键，跳过: {}", before);
                return;
            }

            Long oldId = convertToLong(oldIdObj);
            Long newId = newIdObj != null ? convertToLong(newIdObj) : null;
            String fullTableName = schema + "." + table;

            try {
                if (newId != null && newId.equals(oldId)) {
                    // 情况1: id 未变，直接更新
                    int affected = DatabaseOperator.updateById(schema, table, newId, after);
                    if (affected == 0) {
                        log.warn("UPDATE未影响任何行，可能目标表缺少记录: id={}", newId);
                    }
                } else if (newId != null) {
                    // 情况2: id 被修改 → 先删旧，再插新
                    DatabaseOperator.executeDelete(fullTableName, before); // 删除旧主键
                    DatabaseOperator.executeInsert(fullTableName, after);  // 插入新数据
                    log.info("主键变更: id 从 {} 改为 {}，执行 delete+insert", oldId, newId);
                } else {
                    // 情况3: 新 id 为空（非法）→ 删除旧记录？
                    log.warn("UPDATE后id为null，可能是删除操作被误标为update: {}", after);
                    DatabaseOperator.executeDelete(fullTableName, before);
                }
            } catch (Exception e) {
                log.error("UPDATE处理失败: {}.{}", database, table, e);
            }
        }

        private Long convertToLong(Object value) {
            if (value == null) return null;
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
            try {
                return Long.parseLong(value.toString());
            } catch (Exception e) {
                log.warn("无法转换为 Long: {}", value);
                return null;
            }
        }

        private void handleDelete(JSONObject cdcEvent, String database, String table) {

            String schema = targetDatabase.getSchema();

            JSONObject before = cdcEvent.getJSONObject("before");
            log.info("处理DELETE事件: {}.{}, 删除数据: {}", database, table, before);

            try {
                // 从目标数据库的同名表删除
                DatabaseOperator.executeDelete(schema + "." + table, before);
            } catch (Exception e) {
                log.error("DELETE操作失败: {}.{}", database, table, e);
                // 可以选择重试或记录错误
            }
        }
    }
}
